<script setup>
import dayjs from 'dayjs';
import localeData from 'dayjs/plugin/localeData';
import { keyBy } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { onMounted } from 'vue';
import { useModal } from 'vue-final-modal';
import HawkDeletePopup from '~/common/components/organisms/hawk-delete-popup.vue';
import HawkHandsOnTable from '~/common/components/organisms/hawk-handsontable/hawk-handsontable.vue';
import { useProjectManagementStore } from '~/project-management/store/pm.store';

const props = defineProps({
  parentActivityId: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['close']);

dayjs.extend(localeData);

const $t = inject('$t');

const project_management_store = useProjectManagementStore();
const { $g, active_schedule_data } = storeToRefs(project_management_store);
const { update_custom_planned_progress, get_custom_planned_progress } = project_management_store;

const state = reactive({
  interval: 'monthly',
  master_data: {}, // Structure: { '<activity_id>': { '<date>': <value> } }
  current_value: dayjs().year(), // Stores year (2025) in the case of year and week, and dayjs object in the case of day
  activity_hierarchy_map: {}, // Structure: { '<parent>': { '<child>': { '<grandchild>': {} } } }
  hot_instance: null,
  hot_data: [],
  longest_wbs_code_length: 0,
  is_loading: false,
});

let custom_planned_progress_map = {};

const info_modal = useModal({
  component: HawkDeletePopup,
  attrs: {
    button_text: 'Continue',
    button_color: 'primary',
    header_icon: IconHawkDashboardPreviewInfo,
    onClose() {
      info_modal.close();
    },
  },
});

const hot_columns = computed(() => {
  let dynamic_date_items = [];
  if (state.interval === 'monthly') {
    dynamic_date_items = dayjs.months().map((month, index) => {
      return {
        data: `${state.current_value}-${String(index + 1).padStart(2, '0')}-01`,
        header: month,
        width: '150px',
      };
    });
  }
  else if (state.interval === 'weekly') {
    const start_of_year = dayjs().year(state.current_value).startOf('year');
    dynamic_date_items = Array.from({ length: 53 }, (_, i) => {
      const start_of_week = start_of_year.add(i, 'week').startOf('week');
      if (start_of_week.year() !== state.current_value)
        return null;
      return {
        data: start_of_week.format('YYYY-MM-DD'),
        header: `${start_of_week.format('DD MMM')}`,
        width: '130px',
      };
    }).filter(Boolean);
  }
  else if (state.interval === 'daily') {
    const daysInMonth = state.current_value.daysInMonth();
    dynamic_date_items = Array.from({ length: daysInMonth }, (_, i) => {
      const day = i + 1;
      return {
        data: `${state.current_value.format('YYYY-MM')}-${String(day).padStart(2, '0')}`,
        header: day,
        width: '100px',
      };
    });
  }
  dynamic_date_items = dynamic_date_items.map(item => ({
    ...item,
    type: 'numeric',
    config: {
      field_type: 'numeric',
      min: 0,
    },
    validator: 'default-validator',
  }));
  return [
    {
      data: 'text',
      header: $t('Activity'),
      readOnly: true,
    },
    ...dynamic_date_items,
  ];
});

const hot_nested_headers = computed(() => {
  let group_labels;
  if (state.interval === 'monthly') {
    group_labels = [{ label: state.current_value, colspan: 12 }];
  }
  else if (state.interval === 'weekly') {
    const start_of_year = dayjs().year(state.current_value).startOf('year');
    const end_of_year = dayjs().year(state.current_value).endOf('year');
    const month_groups = [];
    let current_month = null;
    let current_colspan = 0;
    let start_of_week = start_of_year.startOf('week');
    while (start_of_week.isSameOrBefore(end_of_year, 'day')) {
      if (start_of_week.year() !== state.current_value) {
        start_of_week = start_of_week.add(1, 'week');
        continue;
      }
      const month_name = start_of_week.format('MMMM');
      if (month_name !== current_month) {
        if (current_month !== null) {
          month_groups.push({ label: current_month, colspan: current_colspan });
        }
        current_month = month_name;
        current_colspan = 1;
      }
      else {
        current_colspan++;
      }
      start_of_week = start_of_week.add(1, 'week');
    }
    if (current_month !== null && current_colspan > 0) {
      month_groups.push({ label: current_month, colspan: current_colspan });
    }
    group_labels = month_groups;
  }
  else if (state.interval === 'daily') {
    group_labels = [{ label: dayjs().month(state.current_value.month()).format('MMMM'), colspan: state.current_value.daysInMonth() }];
  }
  return [
    [$t('Activity'), ...group_labels],
    ['', ...hot_columns.value.slice(1).map(column => column.header)],
  ];
});

const interval_options = [
  { value: 'monthly', label: $t('Monthly'), on_click: () => handleIntervalChange('monthly') },
  { value: 'weekly', label: $t('Weekly'), on_click: () => handleIntervalChange('weekly') },
  { value: 'daily', label: $t('Daily'), on_click: () => handleIntervalChange('daily') },
];

function getContentText(current_interval, new_interval) {
}


function handleIntervalChange(new_interval) {
  const current_interval = state.interval;
  info_modal.patchOptions({
    attrs: {
      header: `${$t('Switching from')} ${$t(current_interval)} ${$t('to')} ${$t(new_interval)}}`,
      content: 'heeh',
      confirm: () => {
        state.interval = new_interval;
        info_modal.close();
      },
    },
  });
  info_modal.open();
}

function handleRange(direction) {
  state.hot_instance = null;
  if (state.interval === 'monthly' || state.interval === 'weekly') {
    state.current_value += direction;
  }
  else if (state.interval === 'daily') {
    state.current_value = state.current_value.add(direction, 'month');
  }
}

function createActivityHierarchy(parent_id, parent_obj) {
  const children = active_schedule_data.value.data.filter(activity => activity.parent === parent_id);
  children.forEach((child) => {
    parent_obj[child.id] = {};
    createActivityHierarchy(child.id, parent_obj[child.id]);
  });
}

function buildHotDataFromHierarchy(map) {
  return Object.entries(map).map(([id, children]) => {
    const task = $g.value.getTask(id);
    let node = { id, text: task.text };
    state.longest_wbs_code_length = Math.max(state.longest_wbs_code_length, $g.value.getWBSCode(task).length);
    if (custom_planned_progress_map[id]) {
      node = { ...node, ...custom_planned_progress_map[id].custom_planned_progress_data };
    }
    if (children && Object.keys(children).length > 0) {
      node.__children = buildHotDataFromHierarchy(children);
    }
    return node;
  });
}

function cellsConfiguration(row) {
  const cellProperties = {};
  const rowData = state.hot_instance?.getSourceDataAtRow(row);

  if (rowData?.__children && rowData.__children.length > 0) {
    cellProperties.readOnly = true;
    cellProperties.className = 'read-only-cell';
  }

  return cellProperties;
}

function afterChange(changes) {
  if (!changes || !state.hot_instance)
    return;
  changes.forEach((change) => {
    const row_data = state.hot_instance?.getSourceDataAtRow(state.hot_instance?.toPhysicalRow(change[0]));
    if (!state.master_data[row_data.id])
      state.master_data[row_data.id] = {};
    state.master_data[row_data.id][change[1]] = change[3];
  });
}

function rowHeaders(row) {
  const row_data = state.hot_instance?.getSourceDataAtRow(row);
  if (row_data?.id)
    return $g.value.getWBSCode($g.value.getTask(row_data.id));
  return null;
}

function onSave() {
  logger.log('onSave', state.master_data);
  const payload = [];
  Object.keys(state.master_data).forEach((activity_id) => {
    payload.push({
      id: activity_id,
      custom_planned_progress_data: state.master_data[activity_id],
      custom_planned_progress_config: {
        interval: state.interval,
      },
    });
  });
  if (payload.length)
    update_custom_planned_progress(payload);
}

watch(() => state.interval, () => {
  state.hot_instance = null;
  if (state.interval === 'monthly' || state.interval === 'weekly')
    state.current_value = dayjs().year();
  else if (state.interval === 'daily')
    state.current_value = dayjs();
});

onMounted(async () => {
  state.is_loading = true;
  const data = await get_custom_planned_progress();
  const filtered_data = data.filter(custom_planned_progress => custom_planned_progress.planned_progress_completion_type === 'CUSTOM');
  state.interval = filtered_data[0]?.custom_planned_progress_config?.interval || 'monthly';
  custom_planned_progress_map = keyBy(filtered_data, 'id');
  state.master_data = filtered_data.reduce((acc, curr) => {
    acc[curr.id] = curr.custom_planned_progress_data;
    return acc;
  }, {});
  state.activity_hierarchy_map[props.parentActivityId] = {};
  createActivityHierarchy(props.parentActivityId, state.activity_hierarchy_map[props.parentActivityId]);
  state.hot_data = buildHotDataFromHierarchy(state.activity_hierarchy_map);
  state.is_loading = false;
});

// TODO: Handling changing of intervals. Switching between states, summing stuff, etc.
</script>

<template>
  <HawkModalContainer content_class="rounded-none w-full h-full">
    <Vueform
      size="sm"
      :display-errors="false"
      :display-messages="false"
      :columns="{
        default: {
          container: 12,
          label: 3,
          wrapper: 9,
        },
        sm: {
          label: 4,
        },
        md: {
          label: 4,
        },
        lg: {
          label: 4,
        },
      }"
    >
      <div class="col-span-12">
        <HawkModalHeader @close="emit('close')">
          <template #title>
            <div class="flex flex-col justify-start">
              {{ $t('Set planned values') }}
            </div>
          </template>
          <template #right>
            <div class="h-full flex items-center gap-3">
              <div class="text-xs font-medium text-gray-900 flex items-center gap-3">
                <IconHawkChevronLeft class="cursor-pointer" @click="handleRange(-1)" />
                <template v-if="state.interval === 'monthly' || state.interval === 'weekly'">
                  {{ state.current_value }}
                </template>
                <template v-else-if="state.interval === 'daily'">
                  {{ dayjs().month(state.current_value.month()).format('MMMM') }} {{ state.current_value.year() }}
                </template>
                <IconHawkChevronRight class="cursor-pointer" @click="handleRange(1)" />
              </div>
              <HawkMenu position="fixed" :items="interval_options" additional_trigger_classes="!ring-0" class="-mt-1">
                <template #trigger="{ open }">
                  <div class="flex items-center gap-1">
                    <span class="text-xs font-normal text-gray-500">
                      {{ $t('Interval') }}:
                    </span>
                    <span class="text-xs font-medium text-gray-900">
                      {{ interval_options.find((option) => option.value === state.interval)?.label }}
                    </span>
                    <IconHawkChevronUp v-if="open" />
                    <IconHawkChevronDown v-else />
                  </div>
                </template>
              </HawkMenu>
            </div>
          </template>
        </HawkModalHeader>
        <HawkModalContent class="!h-[calc(100vh-160px)] !max-h-[calc(100vh-160px)] pm-set-planned-values-content">
          <HawkLoader v-if="state.is_loading" container_class="h-full" />
          <HawkHandsOnTable
            v-else
            :key="`${state.interval}-${state.current_value}`"
            :hot-settings="{
              rowHeaders,
              afterChange,
              nestedRows: true,
              bindRowsWithHeaders: true,
              nestedHeaders: hot_nested_headers,
              rowHeaderWidth: state.longest_wbs_code_length * 15,
              cells: cellsConfiguration,
            }"
            :data="state.hot_data"
            :columns="hot_columns"
            :columns-menu="{ items: {} }"
            height="100%"
            @ready="state.hot_instance = $event"
          />
        </HawkModalContent>
        <HawkModalFooter>
          <template #right>
            <Vueform size="sm">
              <div class="flex justify-end w-full col-span-full">
                <ButtonElement
                  name="cancel"
                  class="mr-4"
                  :secondary="true"
                  @click="emit('close')"
                >
                  {{ $t('Cancel') }}
                </ButtonElement>
                <ButtonElement
                  name="save"
                  @click="onSave"
                >
                  {{ $t('Save') }}
                </ButtonElement>
              </div>
            </vueform>
          </template>
        </HawkModalFooter>
      </div>
    </Vueform>
  </HawkModalContainer>
</template>

<style lang="scss">
.pm-set-planned-values-content {
  .ht_nestingCollapse::before {
    mask-image:  url('data:image/svg+xml,%3Csvg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M4 6L8 10L12 6" stroke="%23475467" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/%3E%3C/svg%3E') !important;
  }

  .ht_nestingExpand::before {
    mask-image: url('data:image/svg+xml,%3Csvg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M9 18L15 12L9 6" stroke="%23475467" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/%3E%3C/svg%3E') !important;
  }

  .ht_nestingButton {
    background-color: transparent !important;
    box-shadow: none !important;
  }
}
</style>
